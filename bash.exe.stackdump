Stack trace:
Frame         Function      Args
0007FFFF7A00  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF6900) msys-2.0.dll+0x1FE8E
0007FFFF7A00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7CD8) msys-2.0.dll+0x67F9
0007FFFF7A00  000210046832 (000210286019, 0007FFFF78B8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7A00  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7A00  000210068E24 (0007FFFF7A10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF7CE0  00021006A225 (0007FFFF7A10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB8E9C0000 ntdll.dll
7FFB8C750000 KERNEL32.DLL
7FFB8BAF0000 KERNELBASE.dll
7FFB8E680000 USER32.dll
7FFB8C3E0000 win32u.dll
7FFB8DCE0000 GDI32.dll
000210040000 msys-2.0.dll
7FFB8C120000 gdi32full.dll
7FFB8BEF0000 msvcp_win.dll
7FFB8C600000 ucrtbase.dll
7FFB8C8C0000 advapi32.dll
7FFB8C980000 msvcrt.dll
7FFB8CA30000 sechost.dll
7FFB8E460000 RPCRT4.dll
7FFB8B0C0000 CRYPTBASE.DLL
7FFB8C560000 bcryptPrimitives.dll
7FFB8D640000 IMM32.DLL
