import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import screens
import SplashScreen from '../screens/SplashScreen';
import OnboardingScreen from '../screens/OnboardingScreen';
import LoginScreen from '../screens/LoginScreen';
import RegisterScreen from '../screens/RegisterScreen';
import HomeScreen from '../screens/HomeScreen';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [initialRoute, setInitialRoute] = useState('Splash');

  useEffect(() => {
    checkAppState();
  }, []);

  const checkAppState = async () => {
    try {
      const [hasSeenOnboarding, isLoggedIn] = await Promise.all([
        AsyncStorage.getItem('hasSeenOnboarding'),
        AsyncStorage.getItem('isLoggedIn'),
      ]);

      // Tentukan route awal berdasarkan status
      if (isLoggedIn === 'true') {
        setInitialRoute('MainApp');
      } else if (hasSeenOnboarding === 'true') {
        setInitialRoute('AuthStack');
      } else {
        setInitialRoute('Splash');
      }
    } catch (error) {
      console.error('Error checking app state:', error);
      setInitialRoute('Splash');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return null; // Atau loading component
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName={initialRoute}
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
      >
        {/* Splash Screen */}
        <Stack.Screen 
          name="Splash" 
          component={SplashScreen}
          options={{
            animationEnabled: false,
          }}
        />

        {/* Onboarding Stack */}
        <Stack.Screen 
          name="OnboardingStack" 
          component={OnboardingNavigator}
          options={{
            animationEnabled: false,
          }}
        />

        {/* Auth Stack */}
        <Stack.Screen 
          name="AuthStack" 
          component={AuthNavigator}
          options={{
            animationEnabled: false,
          }}
        />

        {/* Main App */}
        <Stack.Screen 
          name="MainApp" 
          component={MainNavigator}
          options={{
            animationEnabled: false,
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

// Onboarding Navigator
const OnboardingNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Onboarding" component={OnboardingScreen} />
    </Stack.Navigator>
  );
};

// Auth Navigator
const AuthNavigator = () => {
  return (
    <Stack.Navigator 
      screenOptions={{ 
        headerShown: false,
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
    >
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Register" component={RegisterScreen} />
    </Stack.Navigator>
  );
};

// Main App Navigator
const MainNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Home" component={HomeScreen} />
      {/* Tambahkan screen lain untuk main app di sini */}
    </Stack.Navigator>
  );
};

export default AppNavigator;
