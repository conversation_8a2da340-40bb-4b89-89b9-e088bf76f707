import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export default function OnboardingScreen({ navigation }) {
  return (
    <View style={styles.container}>
      {/* Background pattern, you can use SVG, PNG, or View for lines */}
      <Image
        source={require('./assets/onboarding-bg.png')}
        style={styles.bgPattern}
        resizeMode="cover"
      />

      {/* Title */}
      <View style={styles.titleWrapper}>
        <Text style={styles.title}>Temukan Ruang{'\n'}Kerja Virtual</Text>
      </View>

      {/* Promo cards (illustration) */}
      <View style={styles.cardStack}>
        <Image
          source={require('./assets/promo-card1.png')}
          style={[styles.promoCard, { zIndex: 1, transform: [{ rotate: '-8deg' }, { translateY: 40 }, { translateX: -40 }] }]}
        />
        <Image
          source={require('./assets/promo-card2.png')}
          style={[styles.promoCard, { zIndex: 2, transform: [{ rotate: '4deg' }, { translateY: 10 }, { translateX: 20 }] }]}
        />
        <Image
          source={require('./assets/promo-card3.png')}
          style={[styles.promoCard, { zIndex: 3 }]}
        />
      </View>

      {/* Start Button */}
      <TouchableOpacity
        style={styles.startBtn}
        onPress={() => {
          // navigation.replace('Login'); // Ganti dengan tujuan selanjutnya
        }}
      >
        <Text style={styles.startText}>Mulai</Text>
        <Image
          source={require('./assets/pkbi-logo.png')}
          style={styles.startLogo}
        />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  bgPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    width,
    height,
    zIndex: 0,
    opacity: 1,
  },
  titleWrapper: {
    marginTop: height * 0.13,
    alignItems: 'center',
    zIndex: 2,
  },
  title: {
    color: '#17203A',
    fontSize: 36,
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: 44,
    letterSpacing: 0.2,
  },
  cardStack: {
    width: width,
    height: 260,
    marginTop: 38,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 3,
  },
  promoCard: {
    width: width * 0.81,
    height: 120,
    position: 'absolute',
    borderRadius: 18,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.10,
    shadowRadius: 12,
    elevation: 3,
  },
  startBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 50,
    backgroundColor: 'transparent',
  },
  startText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222',
    marginRight: 10,
  },
  startLogo: {
    width: 38,
    height: 38,
    resizeMode: 'contain',
  },
});