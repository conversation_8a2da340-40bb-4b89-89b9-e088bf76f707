import React, { useEffect } from 'react';
import { View, Text, Image, StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export default function SplashScreen({ navigation }) {
  useEffect(() => {
    // Simulasi splash selama 2 detik sebelum pindah ke halaman lain
    const timer = setTimeout(() => {
      // navigation.replace('Login'); // Ganti dengan tujuan screen berikutnya
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <View style={styles.container}>
      {/* Decorative images, replace require with your local assets if needed */}
      <Image source={require('./assets/splash-call.png')} style={[styles.item, {top: 25, left: -40, width: 180, height: 70}]} />
      <Image source={require('./assets/splash-orangeclip.png')} style={[styles.item, {top: 70, right: 50, width: 85, height: 70}]} />
      <Image source={require('./assets/splash-blueclip.png')} style={[styles.item, {top: 120, right: -30, width: 110, height: 70}]} />
      <Image source={require('./assets/splash-paperstack.png')} style={[styles.item, {top: 220, left: -40, width: 165, height: 120}]} />
      <Image source={require('./assets/splash-pinkpaper.png')} style={[styles.item, {top: 340, right: 40, width: 90, height: 55}]} />
      <Image source={require('./assets/splash-paperclip.png')} style={[styles.item, {bottom: 190, left: -10, width: 80, height: 65}]} />
      <Image source={require('./assets/splash-pen.png')} style={[styles.item, {bottom: 110, right: 60, width: 135, height: 45}]} />
      <Image source={require('./assets/splash-crumple.png')} style={[styles.item, {bottom: 60, left: 30, width: 120, height: 90}]} />

      <View style={styles.logoContainer}>
        <Text style={styles.title1}>Virtual</Text>
        <Text style={styles.title2}>Co-Working Space</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent:'center',
    alignItems:'center'
  },
  item: {
    position: 'absolute',
    resizeMode: 'contain',
    opacity: 1
  },
  logoContainer: {
    position: 'absolute',
    top: height * 0.32,
    left: 0,
    right: 0,
    alignItems: 'center'
  },
  title1: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#1976D2',
    textAlign: 'center'
  },
  title2: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#1976D2',
    textAlign: 'center'
  }
});