import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  FlatList,
  SafeAreaView,
  ScrollView
} from 'react-native';

// Dummy Notification Icon with Badge
const NotificationIcon = () => (
  <View>
    <Text style={{fontSize: 28}}>🔔</Text>
    <View style={{
      position: 'absolute',
      right: -2,
      top: -4,
      backgroundColor: '#FF4D4F',
      borderRadius: 10,
      minWidth: 18,
      height: 18,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 2,
    }}>
      <Text style={{color:'#fff', fontSize:12, fontWeight:'bold'}}>6</Text>
    </View>
  </View>
);

const promoImages = [
  // Replace with your real images
  {id: 'promo1', image: 'https://i.imgur.com/H6b1KqK.png'},
  {id: 'promo2', image: 'https://i.imgur.com/H6b1KqK.png'},
];

const products = [
  {
    id: '1',
    title: 'Aplikasi Data Relawan',
    price: 'Rp.25.000.000',
    image: 'https://i.imgur.com/0y8Ftya.png', // Replace with your image
    rating: 4,
  },
  {
    id: '2',
    title: 'Aplikasi absen',
    price: 'Rp.28.000.000',
    image: 'https://i.imgur.com/0y8Ftya.png',
    rating: 5,
  },
  {
    id: '3',
    title: 'Aplikasi lainnya',
    price: 'Rp.20.000.000',
    image: 'https://i.imgur.com/0y8Ftya.png',
    rating: 3,
  },
  {
    id: '4',
    title: 'Aplikasi komunitas',
    price: 'Rp.21.000.000',
    image: 'https://i.imgur.com/0y8Ftya.png',
    rating: 4,
  },
];

const categories = [
  { id: 'all', label: 'Semua' },
  { id: 'produk', label: 'Produk' },
  { id: 'layanan', label: 'Layanan' }
];

export default function HomeScreen({ navigation }) {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [search, setSearch] = useState('');

  // Dummy filtered data
  const filteredProducts = selectedCategory === 'all'
    ? products
    : products.filter(p => selectedCategory === 'produk');

  return (
    <SafeAreaView style={{flex:1, backgroundColor:'#fff'}}>
      <ScrollView
        contentContainerStyle={{paddingBottom: 90}}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerContainer}>
          <View>
            <Text style={styles.headerTitle}>Temukan Ruang Kerja Virtual</Text>
            <Text style={styles.headerSubtitle}>Untuk Komunitas Anda</Text>
          </View>
          <View style={styles.iconNotif}>
            <NotificationIcon />
          </View>
        </View>

        {/* Search Bar */}
        <View style={styles.searchBar}>
          <TextInput
            style={styles.searchInput}
            placeholder="Cari produk yang anda butuhkan..."
            placeholderTextColor="#888"
            value={search}
            onChangeText={setSearch}
          />
          <Text style={{fontSize: 24, color:'#888'}}>🔍</Text>
        </View>

        {/* Promo Banner */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{marginTop:20}} contentContainerStyle={{paddingLeft:18}}>
          <View style={styles.promoCard}>
            <Image
              source={{uri:'https://i.imgur.com/3kz6H0f.png'}}
              style={styles.promoImage}
              resizeMode="contain"
            />
            <View style={{position:'absolute', left:16, top:18}}>
              <Text style={{fontSize:15, fontWeight:'bold'}}>SPECIAL PROMO</Text>
              <Text style={{fontSize:32, fontWeight:'bold', color:'#1A237E'}}>17th PKBI KEPRI</Text>
              <Text style={{fontSize:13, marginTop:10, color:'#444'}}>Semua Produk dan Layanan{'\n'}Mendapat Potongan Harga <Text style={{color:'#B71C1C', fontWeight:'bold'}}>17%</Text></Text>
            </View>
          </View>
          {/* Bisa tambah promo lain */}
        </ScrollView>

        {/* Katalog */}
        <View style={styles.catalogHeader}>
          <Text style={styles.catalogTitle}>Katalog</Text>
          <TouchableOpacity>
            <Text style={styles.seeAll}>lihat semua</Text>
          </TouchableOpacity>
        </View>
        {/* Category Filter */}
        <View style={styles.categoryRow}>
          {categories.map(cat => (
            <TouchableOpacity
              key={cat.id}
              style={[
                styles.categoryBtn,
                selectedCategory === cat.id && styles.categoryBtnActive
              ]}
              onPress={() => setSelectedCategory(cat.id)}
            >
              <Text style={[
                styles.categoryBtnText,
                selectedCategory === cat.id && styles.categoryBtnTextActive
              ]}>{cat.label}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Product List */}
        <View style={{flexDirection:'row', flexWrap:'wrap', justifyContent:'space-between', paddingHorizontal:18, marginTop:10}}>
          {filteredProducts.map(item => (
            <View key={item.id} style={styles.productCard}>
              <Image source={{uri:item.image}} style={styles.productImg} />
              <View style={{padding:10}}>
                <Text style={styles.productTitle}>{item.title}</Text>
                <Text style={styles.productPrice}>{item.price}</Text>
                <View style={{flexDirection:'row', marginTop:6}}>
                  {[1,2,3,4,5].map(i => (
                    <Text key={i} style={{
                      color: i <= item.rating ? '#FFB300' : '#BDBDBD',
                      fontSize:16
                    }}>★</Text>
                  ))}
                </View>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.tabBar}>
        <TouchableOpacity style={styles.tabBtnActive}>
          <Text style={{fontSize:22, color:'#1976D2'}}>🏠</Text>
          <Text style={{fontSize:15, color:'#1976D2', fontWeight:'bold', marginTop:3}}>Beranda</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.tabBtn}>
          <Text style={{fontSize:22, color:'#222'}}>📦</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.tabBtn}>
          <Text style={{fontSize:22, color:'#222'}}>💬</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.tabBtn}>
          <Text style={{fontSize:22, color:'#222'}}>👤</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    paddingTop: 28,
    paddingHorizontal: 18,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    backgroundColor:'#fff'
  },
  headerTitle: {
    fontSize: 26,
    color: '#1976D2',
    fontWeight: 'bold',
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 18,
    color: '#444',
    marginBottom: 10,
  },
  iconNotif: {
    marginTop: 5,
    marginRight: 4,
  },
  searchBar: {
    backgroundColor: "#fff",
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: "#BDBDBD",
    borderRadius: 26,
    paddingHorizontal: 20,
    marginHorizontal: 18,
    marginTop: 8,
    height: 50,
  },
  searchInput: {
    flex: 1,
    fontSize: 17,
    color: '#222',
    paddingVertical: 6,
  },
  promoCard: {
    width: 340,
    height: 120,
    backgroundColor: "#fff",
    borderRadius: 18,
    marginRight: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    overflow:'hidden',
    justifyContent:'center'
  },
  promoImage: {
    width: '100%',
    height: '100%',
    opacity: 0.18,
    position: 'absolute'
  },
  catalogHeader: {
    marginTop: 30,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 18,
  },
  catalogTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#222'
  },
  seeAll: {
    fontSize: 16,
    color: '#1976D2',
    fontWeight: 'bold'
  },
  categoryRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 18,
    marginBottom: 8,
    marginTop: 2,
  },
  categoryBtn: {
    borderRadius: 18,
    borderWidth: 1,
    borderColor: '#888',
    marginRight: 14,
    paddingHorizontal: 20,
    paddingVertical: 7,
    backgroundColor: '#fff',
  },
  categoryBtnActive: {
    borderColor: '#1976D2',
    backgroundColor: '#E3F1FE'
  },
  categoryBtnText: {
    color: '#222',
    fontWeight: 'bold',
    fontSize: 16
  },
  categoryBtnTextActive: {
    color: '#1976D2'
  },
  productCard: {
    width: '47%',
    backgroundColor: '#F4F8FB',
    borderRadius: 18,
    marginBottom: 18,
    overflow:'hidden',
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.07,
    shadowRadius: 6,
  },
  productImg: {
    width: '100%',
    height: 140,
    resizeMode: 'contain',
    backgroundColor:'#fff'
  },
  productTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color:'#111'
  },
  productPrice: {
    fontSize: 15,
    color: '#1565C0',
    marginTop: 2
  },
  tabBar: {
    position:'absolute',
    left:0,
    right:0,
    bottom:0,
    paddingTop: 10,
    paddingBottom: 16,
    flexDirection:'row',
    backgroundColor:'#fff',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    elevation:15,
    justifyContent:'space-around',
    alignItems:'center'
  },
  tabBtn: {
    alignItems:'center',
    flex:1
  },
  tabBtnActive: {
    backgroundColor:'#E3F1FE',
    borderRadius:18,
    alignItems:'center',
    flex:1,
    paddingVertical: 7,
    marginHorizontal: 10
  }
});