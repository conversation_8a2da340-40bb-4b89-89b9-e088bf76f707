import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  Image, 
  SafeAreaView,
  ScrollView
} from 'react-native';

// Dummy icons, replace with react-native-vector-icons or custom SVG/icon
const UserIcon = () => (
  <Text style={{fontSize: 20, marginRight: 10}}>👤</Text>
);
const LockIcon = () => (
  <Text style={{fontSize: 20, marginRight: 10}}>🔒</Text>
);

export default function LoginScreen({ navigation }) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [securePass, setSecurePass] = useState(true);

  return (
    <SafeAreaView style={{flex:1, backgroundColor:'#fff'}}>
      <ScrollView contentContainerStyle={{flexGrow:1}}>
        {/* Background can be an ImageBackground or gradient for real use */}
        <View style={styles.topBg} />
        <View style={styles.container}>
          <Text style={styles.header1}>Co-Working Space</Text>
          <Text style={styles.title}>Masuk</Text>
          <Text style={styles.subtitle}>Gunakan domain email komunitas anda</Text>

          <View style={styles.formBox}>
            {/* Username */}
            <Text style={styles.label}>Username</Text>
            <View style={styles.inputWrapper}>
              <UserIcon />
              <TextInput 
                style={styles.input}
                placeholder="Masukkan username/email"
                value={username}
                onChangeText={setUsername}
                placeholderTextColor="#BDBDBD"
                autoCapitalize="none"
              />
            </View>

            {/* Password */}
            <Text style={styles.label}>Password</Text>
            <View style={styles.inputWrapper}>
              <LockIcon />
              <TextInput 
                style={styles.input}
                placeholder="Masukkan password"
                value={password}
                onChangeText={setPassword}
                placeholderTextColor="#BDBDBD"
                secureTextEntry={securePass}
                autoCapitalize="none"
              />
              <TouchableOpacity onPress={() => setSecurePass(!securePass)}>
                <Text style={{fontSize: 20}}>👁️</Text>
              </TouchableOpacity>
            </View>

            {/* Button */}
            <TouchableOpacity style={styles.button}>
              <Text style={styles.buttonText}>Masuk</Text>
            </TouchableOpacity>

            {/* Don't have an account */}
            <View style={styles.bottomTextRow}>
              <Text style={styles.bottomText}>Belum mempunyai akun? </Text>
              <TouchableOpacity onPress={() => navigation?.navigate('Register')}>
                <Text style={styles.loginText}>Daftar</Text>
              </TouchableOpacity>
            </View>

            <Text style={styles.orText}>Atau masuk dengan</Text>

            {/* Google Button */}
            <TouchableOpacity style={styles.googleBtn}>
              <Image 
                source={{uri: 'https://upload.wikimedia.org/wikipedia/commons/5/53/Google_%22G%22_Logo.svg'}}
                style={{width:20, height:20, marginRight:10}}
              />
              <Text style={styles.googleBtnText}>Masuk dengan Google</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  topBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 320,
    backgroundColor: '#E8F0FE',
    zIndex: -1,
    borderBottomLeftRadius: 40,
    borderBottomRightRadius: 40,
  },
  container: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 40,
    paddingHorizontal: 0,
  },
  header1: {
    fontSize: 24,
    color: "#1976D2",
    fontWeight: "bold",
    marginBottom: 10,
  },
  title: {
    fontSize: 42,
    fontWeight: 'bold',
    color: "#212121",
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 18,
    color: "#757575",
    marginBottom: 28,
    textAlign: 'center'
  },
  formBox: {
    backgroundColor: "#fff",
    borderRadius: 28,
    width: "90%",
    padding: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.09,
    shadowRadius: 10,
    elevation: 3,
    marginTop: 6,
    marginBottom: 16,
  },
  label: {
    fontWeight: 'bold',
    fontSize: 18,
    color: "#1976D2",
    marginTop: 14,
    marginBottom: 6,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 18,
    paddingHorizontal: 12,
    marginBottom: 2,
    backgroundColor: "#fff",
    height: 54,
  },
  input: {
    flex: 1,
    fontSize: 17,
    color: "#222",
    paddingVertical: 10,
  },
  button: {
    backgroundColor: "#1565C0",
    borderRadius: 18,
    marginTop: 26,
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  buttonText: {
    color: "#fff",
    fontSize: 22,
    fontWeight: "bold",
  },
  bottomTextRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 6,
  },
  bottomText: {
    fontSize: 16,
    color: "#212121"
  },
  loginText: {
    fontSize: 16,
    color: "#1976D2",
    fontWeight: "bold",
  },
  orText: {
    textAlign: 'center',
    marginVertical: 8,
    fontSize: 16,
    color: "#757575"
  },
  googleBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: "#E0E0E0",
    borderRadius: 18,
    paddingVertical: 10,
    paddingHorizontal: 14,
    justifyContent: 'center',
    marginTop: 4
  },
  googleBtnText: {
    fontSize: 18,
    color: "#212121",
    fontWeight: "500"
  },
});